#!/usr/bin/env python3
"""
分析VCBNet两个分支的单独输出
提取out1 (U-Net分支)、out2 (VAN分支) 和最终融合结果out的差异
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 导入模型和数据加载器
from model import VCBNet
from API.dataloader import RadarSets
from torch.utils.data import DataLoader


class VCBNetAnalyzer(VCBNet):
    """
    继承VCBNet，修改forward方法以返回两个分支的单独输出
    """
    def forward(self, x, return_branches=False):
        # VAN分支 (ConvNeXt骨干网络)
        xx = self.backbone(x)
        stage1, stage2, stage3, stage4 = xx

        up4 = self.up4(stage4)
        up4 = torch.cat([up4, stage3], dim=1)
        up4 = self.up4_(up4)

        up3 = self.up3(up4)
        up3 = torch.cat([up3, stage2], dim=1)
        up3 = self.up3_(up3)

        up2 = self.up2(up3)
        up2 = torch.cat([up2, stage1], dim=1)
        up2 = self.up2_(up2)

        out = self.up1(up2)
        out2 = self.cls_reg(out)  # VAN分支输出

        # U-Net分支
        x1_1 = self.conv(x)
        x1_2 = self.down1_1(x1_1)
        x1_3 = self.down1_2(x1_2)
        x1_4 = self.down1_3(x1_3)
        x1_5 = self.down1_4(x1_4)
        x1_6 = self.up1_1(x1_5, x1_4)
        x1_7 = self.up1_2(x1_6, x1_3)
        x1_8 = self.up1_3(x1_7, x1_2)
        x1_9 = self.up1_4(x1_8, x1_1)
        out1 = self.out(x1_9)  # U-Net分支输出

        # 融合两个分支的输出
        final_out = out1 * 0.5 + out2 * 0.5

        if return_branches:
            return final_out, out1, out2
        else:
            return final_out


def load_model_and_analyze(model_dir, data_path, device='auto'):
    """
    加载模型并分析两个分支的输出
    """
    # 设备选择
    if device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)
    print(f"使用设备: {device}")
    
    # 1. 加载模型配置
    config_path = os.path.join(model_dir, 'model_param.json')
    with open(config_path, 'r') as f:
        config = json.load(f)
    print(f"模型配置: {config['model_type']}")
    
    # 2. 创建分析器模型
    model = VCBNetAnalyzer(n_channels=config['in_shape'][0], n_outputs=config['out_chans'])
    model = model.to(device)
    
    # 3. 加载checkpoint
    checkpoint_path = os.path.join(model_dir, 'checkpoints', 'checkpoint.pth')
    print(f"加载checkpoint: {checkpoint_path}")
    
    checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
    state_dict = checkpoint['model']
    
    # 处理accelerator保存的模型
    new_state_dict = {}
    for key, value in state_dict.items():
        if key.startswith('_orig_mod.'):
            new_key = key[10:]
        else:
            new_key = key
        new_state_dict[new_key] = value
    
    # 加载权重
    missing_keys, unexpected_keys = model.load_state_dict(new_state_dict, strict=False)
    print(f"成功加载参数: {len(new_state_dict) - len(missing_keys)}/{len(new_state_dict)}")
    
    # 4. 创建数据集并预测
    img_size = (config['in_shape'][1], config['in_shape'][2])
    dataset = RadarSets(data_path, img_size, mode='test', data_type='vis')
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    model.eval()
    
    with torch.no_grad():
        for inputs, targets in dataloader:
            inputs = inputs.to(device)
            
            # 获取两个分支的输出
            final_out, unet_out, van_out = model(inputs, return_branches=True)
            
            # 转换为numpy
            final_np = final_out.cpu().numpy()
            unet_np = unet_out.cpu().numpy()
            van_np = van_out.cpu().numpy()
            ground_truth = targets.numpy()
            
            return final_np, unet_np, van_np, ground_truth


def analyze_outputs(final_out, unet_out, van_out, ground_truth, sample_name):
    """
    分析三个输出的统计特性
    """
    print(f"\n=== {sample_name} 分支输出分析 ===")
    
    # 基本统计
    outputs = {
        'U-Net Branch (out1)': unet_out[0, 0],
        'VAN Branch (out2)': van_out[0, 0], 
        'Final Output (out)': final_out[0, 0],
        'Ground Truth': ground_truth[0, 0]
    }
    
    print("\n数值范围统计:")
    print("-" * 60)
    print(f"{'Branch':<20} {'Min':<12} {'Max':<12} {'Mean':<12} {'Std':<12}")
    print("-" * 60)
    
    for name, data in outputs.items():
        print(f"{name:<20} {data.min():<12.6f} {data.max():<12.6f} {data.mean():<12.6f} {data.std():<12.6f}")
    
    # 分析分支差异
    print(f"\n分支差异分析:")
    print("-" * 40)
    
    diff_unet_van = unet_out[0, 0] - van_out[0, 0]
    print(f"U-Net - VAN 差异:")
    print(f"  最大差异: {diff_unet_van.max():.6f}")
    print(f"  最小差异: {diff_unet_van.min():.6f}")
    print(f"  平均差异: {diff_unet_van.mean():.6f}")
    print(f"  差异标准差: {diff_unet_van.std():.6f}")
    
    # 验证融合公式
    expected_final = (unet_out[0, 0] + van_out[0, 0]) / 2
    fusion_diff = final_out[0, 0] - expected_final
    print(f"\n融合验证 (final_out vs (out1+out2)/2):")
    print(f"  最大差异: {fusion_diff.max():.10f}")
    print(f"  是否完全一致: {np.allclose(final_out[0, 0], expected_final)}")
    
    return outputs, diff_unet_van


def visualize_branch_comparison(outputs, diff_unet_van, sample_name, save_dir='branch_analysis'):
    """
    可视化分支对比
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建2x3的子图布局
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 第一行：三个输出
    # U-Net分支
    im1 = axes[0, 0].imshow(outputs['U-Net Branch (out1)'] * 60, cmap='jet', vmin=0, vmax=70)
    axes[0, 0].set_title('U-Net Branch Output (out1)')
    axes[0, 0].axis('off')
    plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)
    
    # VAN分支
    im2 = axes[0, 1].imshow(outputs['VAN Branch (out2)'] * 60, cmap='jet', vmin=0, vmax=70)
    axes[0, 1].set_title('VAN Branch Output (out2)')
    axes[0, 1].axis('off')
    plt.colorbar(im2, ax=axes[0, 1], shrink=0.8)
    
    # 最终输出
    im3 = axes[0, 2].imshow(outputs['Final Output (out)'] * 60, cmap='jet', vmin=0, vmax=70)
    axes[0, 2].set_title('Final Output (out1*0.5 + out2*0.5)')
    axes[0, 2].axis('off')
    plt.colorbar(im3, ax=axes[0, 2], shrink=0.8)
    
    # 第二行：差异分析和真值
    # 分支差异
    im4 = axes[1, 0].imshow(diff_unet_van, cmap='RdBu_r', 
                           vmin=-np.abs(diff_unet_van).max(), 
                           vmax=np.abs(diff_unet_van).max())
    axes[1, 0].set_title('U-Net - VAN Difference')
    axes[1, 0].axis('off')
    plt.colorbar(im4, ax=axes[1, 0], shrink=0.8)
    
    # 真值
    im5 = axes[1, 1].imshow(outputs['Ground Truth'] * 60, cmap='jet', vmin=0, vmax=70)
    axes[1, 1].set_title('Ground Truth')
    axes[1, 1].axis('off')
    plt.colorbar(im5, ax=axes[1, 1], shrink=0.8)
    
    # 统计直方图
    axes[1, 2].hist(outputs['U-Net Branch (out1)'].flatten(), bins=50, alpha=0.7, label='U-Net', color='blue')
    axes[1, 2].hist(outputs['VAN Branch (out2)'].flatten(), bins=50, alpha=0.7, label='VAN', color='red')
    axes[1, 2].hist(outputs['Final Output (out)'].flatten(), bins=50, alpha=0.7, label='Final', color='green')
    axes[1, 2].set_xlabel('Output Value')
    axes[1, 2].set_ylabel('Frequency')
    axes[1, 2].set_title('Output Distribution')
    axes[1, 2].legend()
    axes[1, 2].set_yscale('log')
    
    plt.suptitle(f'VCBNet Branch Analysis - {sample_name}', fontsize=16)
    plt.tight_layout()
    
    save_path = os.path.join(save_dir, f'{sample_name}_branch_analysis.png')
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"保存分支分析图: {save_path}")


def main():
    # 配置参数
    sample_name = "202210090536"
    model_dir = "721/vcbnet0"
    data_path = f"data/test/{sample_name}_match.npy"
    
    print(f"=== 分析样本 {sample_name} 的分支输出 ===")
    
    # 检查文件是否存在
    if not os.path.exists(data_path):
        print(f"❌ 测试数据不存在: {data_path}")
        return
    
    # 1. 加载模型并分析
    print("\n1. 加载模型并提取分支输出...")
    try:
        final_out, unet_out, van_out, ground_truth = load_model_and_analyze(model_dir, data_path)
        print(f"✅ 分析完成")
        print(f"   Final output shape: {final_out.shape}")
        print(f"   U-Net output shape: {unet_out.shape}")
        print(f"   VAN output shape: {van_out.shape}")
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        return
    
    # 2. 统计分析
    print("\n2. 进行统计分析...")
    outputs, diff_unet_van = analyze_outputs(final_out, unet_out, van_out, ground_truth, sample_name)
    
    # 3. 生成可视化
    print("\n3. 生成可视化分析...")
    visualize_branch_comparison(outputs, diff_unet_van, sample_name)
    
    print(f"\n=== 分析完成 ===")


if __name__ == "__main__":
    main()
