#!/usr/bin/env python3
"""
分析VCBNet两个分支的输出分布
"""

import torch
import numpy as np
from model import VCBNet
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import TwoSlopeNorm

def analyze_branch_outputs(model_path, data_path):
    # 加载模型
    model = VCBNet(n_channels=3, n_outputs=1)
    checkpoint = torch.load(model_path, map_location='cpu')

    # 检查checkpoint结构
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint

    # 处理 _orig_mod. 前缀
    new_state_dict = {}
    for key, value in state_dict.items():
        if key.startswith('_orig_mod.'):
            new_key = key[10:]  # 移除 '_orig_mod.' 前缀
            new_state_dict[new_key] = value
        else:
            new_state_dict[key] = value

    # 获取模型的状态字典用于对比
    model_state_dict = model.state_dict()

    # 加载权重并获取详细信息
    missing_keys, unexpected_keys = model.load_state_dict(new_state_dict, strict=False)

    # 统计加载情况
    total_model_params = len(model_state_dict)
    loaded_params = len(new_state_dict)
    missing_params = len(missing_keys)

    print(f"\n=== 模型权重加载统计 ===")
    print(f"✅ 成功加载参数: {loaded_params}/{total_model_params} ({loaded_params/total_model_params:.1%})")

    if missing_params > 0:
        print(f"⚠️  未加载参数: {missing_params} 个")
        if missing_params <= 5:
            for key in missing_keys:
                print(f"     - {key}")
        else:
            print(f"   显示前3个缺失参数:")
            for key in missing_keys[:3]:
                print(f"     - {key}")
            print(f"     ... 还有 {missing_params-3} 个")

    if missing_params == 0:
        print("🎉 所有模型参数都已成功加载！")
    elif loaded_params / total_model_params >= 0.95:
        print("✅ 大部分参数已加载，模型应该可以正常工作")
    else:
        print("⚠️  加载的参数比例较低，请检查模型兼容性")
    model.eval()
    
    # 存储输出
    branch_outputs = {}
    
    def capture_output(name):
        def hook(module, input, output):
            branch_outputs[name] = output.detach().clone()
        return hook
    
    # 注册hooks
    model.out.register_forward_hook(capture_output('unet'))
    model.cls_reg.register_forward_hook(capture_output('van'))
    
    # 加载数据
    data = np.load(data_path)
    input_tensor = torch.from_numpy(data[:3]).unsqueeze(0).float()
    
    # 推理
    with torch.no_grad():
        final_out = model(input_tensor)
    
    # 分析结果
    unet_out = branch_outputs['unet']
    van_out = branch_outputs['van']
    
    print("=== 分支输出分析 ===")
    print(f"UNet分支 (out1): mean={unet_out.mean():.4f}, std={unet_out.std():.4f}, min={unet_out.min():.4f}, max={unet_out.max():.4f}")
    print(f"VAN分支 (out2):  mean={van_out.mean():.4f}, std={van_out.std():.4f}, min={van_out.min():.4f}, max={van_out.max():.4f}")
    print(f"融合输出:        mean={final_out.mean():.4f}, std={final_out.std():.4f}, min={final_out.min():.4f}, max={final_out.max():.4f}")
    
    # 计算实际贡献
    unet_contrib = torch.abs(unet_out * 0.5).mean()
    van_contrib = torch.abs(van_out * 0.5).mean()
    total_contrib = unet_contrib + van_contrib

    print(f"\n=== 实际贡献比例 ===")
    print(f"UNet实际贡献: {unet_contrib/total_contrib:.2%}")
    print(f"VAN实际贡献:  {van_contrib/total_contrib:.2%}")

    # 分析正负值分布
    unet_positive_ratio = (unet_out > 0).float().mean()
    van_positive_ratio = (van_out > 0).float().mean()
    van_negative_ratio = (van_out < 0).float().mean()

    print(f"\n=== 正负值分析 ===")
    print(f"UNet正值比例: {unet_positive_ratio:.2%}")
    print(f"VAN正值比例:  {van_positive_ratio:.2%}")
    print(f"VAN负值比例:  {van_negative_ratio:.2%}")

    # 分析互补性
    unet_high = unet_out > unet_out.mean()
    van_low = van_out < van_out.mean()
    complementary_pixels = (unet_high & van_low).float().mean()

    print(f"\n=== 互补性分析 ===")
    print(f"UNet高值&VAN低值的像素比例: {complementary_pixels:.2%}")

    # 验证融合公式
    manual_fused = unet_out * 0.5 + van_out * 0.5
    diff = torch.abs(final_out - manual_fused).max()
    print(f"\n=== 融合验证 ===")
    print(f"手动融合与模型输出差异: {diff:.6f}")

    return unet_out, van_out, final_out

def plot_branch_outputs(unet_out, van_out, final_out, save_path='branch_outputs_analysis.png'):
    """Plot branch outputs comparison"""
    # 转换为numpy数组用于绘图
    unet_np = unet_out[0, 0].cpu().numpy()
    van_np = van_out[0, 0].cpu().numpy()
    final_np = final_out[0, 0].cpu().numpy()

    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('VCBNet Dual-Branch Output Analysis', fontsize=16, fontweight='bold')

    # First row: Output distribution maps
    # UNet branch output
    im1 = axes[0, 0].imshow(unet_np, cmap='viridis', aspect='auto')
    axes[0, 0].set_title(f'UNet Branch Output\nRange: [{unet_np.min():.3f}, {unet_np.max():.3f}]')
    axes[0, 0].set_xlabel('Width')
    axes[0, 0].set_ylabel('Height')
    plt.colorbar(im1, ax=axes[0, 0])

    # VAN branch output (use two-slope colormap to highlight positive/negative values)
    norm = TwoSlopeNorm(vmin=van_np.min(), vcenter=0, vmax=van_np.max())
    im2 = axes[0, 1].imshow(van_np, cmap='RdBu_r', norm=norm, aspect='auto')
    axes[0, 1].set_title(f'VAN Branch Output\nRange: [{van_np.min():.3f}, {van_np.max():.3f}]')
    axes[0, 1].set_xlabel('Width')
    axes[0, 1].set_ylabel('Height')
    plt.colorbar(im2, ax=axes[0, 1])

    # Fused output
    im3 = axes[0, 2].imshow(final_np, cmap='viridis', aspect='auto')
    axes[0, 2].set_title(f'Fused Output\nRange: [{final_np.min():.3f}, {final_np.max():.3f}]')
    axes[0, 2].set_xlabel('Width')
    axes[0, 2].set_ylabel('Height')
    plt.colorbar(im3, ax=axes[0, 2])

    # Second row: Statistical analysis plots
    # Value distribution histogram
    axes[1, 0].hist(unet_np.flatten(), bins=50, alpha=0.7, label='UNet', color='blue', density=True)
    axes[1, 0].hist(van_np.flatten(), bins=50, alpha=0.7, label='VAN', color='red', density=True)
    axes[1, 0].hist(final_np.flatten(), bins=50, alpha=0.7, label='Fused', color='green', density=True)
    axes[1, 0].set_xlabel('Output Value')
    axes[1, 0].set_ylabel('Density')
    axes[1, 0].set_title('Output Value Distribution Comparison')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # Contribution ratio pie chart
    unet_contrib = torch.abs(unet_out * 0.5).mean().item()
    van_contrib = torch.abs(van_out * 0.5).mean().item()
    total_contrib = unet_contrib + van_contrib

    contributions = [unet_contrib/total_contrib, van_contrib/total_contrib]
    labels = ['UNet Branch', 'VAN Branch']
    colors = ['lightblue', 'lightcoral']

    axes[1, 1].pie(contributions, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    axes[1, 1].set_title('Actual Contribution Ratio')

    # 正负值分析
    unet_pos_ratio = (unet_out > 0).float().mean().item()
    van_pos_ratio = (van_out > 0).float().mean().item()
    van_neg_ratio = (van_out < 0).float().mean().item()

    categories = ['UNet\n正值', 'VAN\n正值', 'VAN\n负值']
    ratios = [unet_pos_ratio, van_pos_ratio, van_neg_ratio]
    colors_bar = ['blue', 'red', 'darkred']

    bars = axes[1, 2].bar(categories, ratios, color=colors_bar, alpha=0.7)
    axes[1, 2].set_ylabel('比例')
    axes[1, 2].set_title('正负值分布')
    axes[1, 2].set_ylim(0, 1)

    # 在柱状图上添加数值标签
    for bar, ratio in zip(bars, ratios):
        height = bar.get_height()
        axes[1, 2].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{ratio:.2%}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"📊 分析图表已保存到: {save_path}")
    plt.show()

if __name__ == "__main__":
    model_path = '721/vcbnet0/checkpoints/checkpoint.pth'
    data_path = 'data/test/202210090536_match.npy'

    print("🔍 开始分析VCBNet两个分支的输出...")
    unet_out, van_out, final_out = analyze_branch_outputs(model_path, data_path)

    print("\n📊 生成可视化图表...")
    plot_branch_outputs(unet_out, van_out, final_out)
    print("✅ 分析完成！")
