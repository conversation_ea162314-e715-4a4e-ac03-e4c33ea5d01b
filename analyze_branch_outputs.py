#!/usr/bin/env python3
"""
分析VCBNet两个分支的输出分布
"""

import torch
import numpy as np
from model import VCBNet
import matplotlib.pyplot as plt

def analyze_branch_outputs(model_path, data_path):
    # 加载模型
    model = VCBNet(n_channels=3, n_outputs=1)
    checkpoint = torch.load(model_path, map_location='cpu')

    # 检查checkpoint结构
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint

    # 处理 _orig_mod. 前缀
    new_state_dict = {}
    for key, value in state_dict.items():
        if key.startswith('_orig_mod.'):
            new_key = key[10:]  # 移除 '_orig_mod.' 前缀
            new_state_dict[new_key] = value
        else:
            new_state_dict[key] = value

    model.load_state_dict(new_state_dict)
    model.eval()
    
    # 存储输出
    branch_outputs = {}
    
    def capture_output(name):
        def hook(module, input, output):
            branch_outputs[name] = output.detach().clone()
        return hook
    
    # 注册hooks
    model.out.register_forward_hook(capture_output('unet'))
    model.cls_reg.register_forward_hook(capture_output('van'))
    
    # 加载数据
    data = np.load(data_path)
    input_tensor = torch.from_numpy(data[:3]).unsqueeze(0).float()
    
    # 推理
    with torch.no_grad():
        final_out = model(input_tensor)
    
    # 分析结果
    unet_out = branch_outputs['unet']
    van_out = branch_outputs['van']
    
    print("=== 分支输出分析 ===")
    print(f"UNet分支 (out1): mean={unet_out.mean():.4f}, std={unet_out.std():.4f}, min={unet_out.min():.4f}, max={unet_out.max():.4f}")
    print(f"VAN分支 (out2):  mean={van_out.mean():.4f}, std={van_out.std():.4f}, min={van_out.min():.4f}, max={van_out.max():.4f}")
    print(f"融合输出:        mean={final_out.mean():.4f}, std={final_out.std():.4f}, min={final_out.min():.4f}, max={final_out.max():.4f}")
    
    # 计算实际贡献
    unet_contrib = torch.abs(unet_out * 0.5).mean()
    van_contrib = torch.abs(van_out * 0.5).mean()
    total_contrib = unet_contrib + van_contrib

    print(f"\n=== 实际贡献比例 ===")
    print(f"UNet实际贡献: {unet_contrib/total_contrib:.2%}")
    print(f"VAN实际贡献:  {van_contrib/total_contrib:.2%}")

    # 分析正负值分布
    unet_positive_ratio = (unet_out > 0).float().mean()
    van_positive_ratio = (van_out > 0).float().mean()
    van_negative_ratio = (van_out < 0).float().mean()

    print(f"\n=== 正负值分析 ===")
    print(f"UNet正值比例: {unet_positive_ratio:.2%}")
    print(f"VAN正值比例:  {van_positive_ratio:.2%}")
    print(f"VAN负值比例:  {van_negative_ratio:.2%}")

    # 分析互补性
    unet_high = unet_out > unet_out.mean()
    van_low = van_out < van_out.mean()
    complementary_pixels = (unet_high & van_low).float().mean()

    print(f"\n=== 互补性分析 ===")
    print(f"UNet高值&VAN低值的像素比例: {complementary_pixels:.2%}")

    # 验证融合公式
    manual_fused = unet_out * 0.5 + van_out * 0.5
    diff = torch.abs(final_out - manual_fused).max()
    print(f"\n=== 融合验证 ===")
    print(f"手动融合与模型输出差异: {diff:.6f}")

    return unet_out, van_out, final_out

if __name__ == "__main__":
    model_path = '721/vcbnet0/checkpoints/checkpoint.pth'
    data_path = 'data/test/202210090536_match.npy'
    
    print("🔍 开始分析VCBNet两个分支的输出...")
    unet_out, van_out, final_out = analyze_branch_outputs(model_path, data_path)
    print("✅ 分析完成！")
