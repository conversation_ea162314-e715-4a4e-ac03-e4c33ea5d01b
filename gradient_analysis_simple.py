#!/usr/bin/env python3
"""
简化版梯度分析 - 正确计算参数梯度和特征梯度
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from model import VCBNet
from API.dataloader import RadarSets
from torch.utils.data import DataLoader


def analyze_gradients_correctly(model, data_loader, device):
    """
    正确分析参数梯度和特征梯度
    """
    model.train()
    criterion = torch.nn.MSELoss()
    
    results = {
        'param_gradients': {'van': [], 'unet': []},
        'feature_gradients': {'van': [], 'unet': []},
        'feature_magnitudes': {'van': [], 'unet': []},
        'losses': {'van': [], 'unet': [], 'fusion': []}
    }
    
    for i, (inputs, targets) in enumerate(data_loader):
        if i >= 5:  # 分析前5个batch
            break
            
        inputs, targets = inputs.to(device), targets.to(device)
        
        # === 1. 前向传播获取所有输出 ===
        # VAN分支
        xx = model.backbone(inputs)
        stage1, stage2, stage3, stage4 = xx
        up4 = model.up4(stage4)
        up4 = torch.cat([up4, stage3], dim=1)
        up4 = model.up4_(up4)
        up3 = model.up3(up4)
        up3 = torch.cat([up3, stage2], dim=1)
        up3 = model.up3_(up3)
        up2 = model.up2(up3)
        up2 = torch.cat([up2, stage1], dim=1)
        up2 = model.up2_(up2)
        van_feat = model.up1(up2)
        van_out = model.cls_reg(van_feat)
        
        # U-Net分支
        x1_1 = model.conv(inputs)
        x1_2 = model.down1_1(x1_1)
        x1_3 = model.down1_2(x1_2)
        x1_4 = model.down1_3(x1_3)
        x1_5 = model.down1_4(x1_4)
        x1_6 = model.up1_1(x1_5, x1_4)
        x1_7 = model.up1_2(x1_6, x1_3)
        x1_8 = model.up1_3(x1_7, x1_2)
        unet_feat = model.up1_4(x1_8, x1_1)
        unet_out = model.out(unet_feat)
        
        # 融合输出
        final_out = van_out * 0.5 + unet_out * 0.5
        
        # === 2. 计算各种损失 ===
        van_loss = criterion(van_out, targets)
        unet_loss = criterion(unet_out, targets)
        fusion_loss = criterion(final_out, targets)
        
        results['losses']['van'].append(van_loss.item())
        results['losses']['unet'].append(unet_loss.item())
        results['losses']['fusion'].append(fusion_loss.item())
        
        # === 3. 记录特征大小 ===
        results['feature_magnitudes']['van'].append(van_feat.norm().item())
        results['feature_magnitudes']['unet'].append(unet_feat.norm().item())
        
        # === 4. 计算参数梯度 (对融合损失) ===
        model.zero_grad()
        fusion_loss.backward(retain_graph=True)
        
        # VAN分支参数梯度
        van_param_grad = 0
        van_param_count = 0
        for name, param in model.named_parameters():
            if any(keyword in name for keyword in ['backbone', 'up4', 'up3', 'up2', 'up1', 'cls_reg']):
                if param.grad is not None:
                    van_param_grad += param.grad.norm().item() ** 2
                    van_param_count += 1
        
        # U-Net分支参数梯度
        unet_param_grad = 0
        unet_param_count = 0
        for name, param in model.named_parameters():
            if any(keyword in name for keyword in ['conv', 'down1', 'up1_', 'out']):
                if param.grad is not None:
                    unet_param_grad += param.grad.norm().item() ** 2
                    unet_param_count += 1
        
        results['param_gradients']['van'].append(np.sqrt(van_param_grad))
        results['param_gradients']['unet'].append(np.sqrt(unet_param_grad))
        
        # === 5. 计算特征梯度 (对融合损失) ===
        # 清除之前的梯度
        model.zero_grad()
        
        # 重新计算，但这次计算特征梯度
        van_feat_grad = torch.autograd.grad(
            outputs=fusion_loss,
            inputs=van_feat,
            retain_graph=True,
            create_graph=False
        )[0]
        
        unet_feat_grad = torch.autograd.grad(
            outputs=fusion_loss,
            inputs=unet_feat,
            retain_graph=False,
            create_graph=False
        )[0]
        
        results['feature_gradients']['van'].append(van_feat_grad.norm().item())
        results['feature_gradients']['unet'].append(unet_feat_grad.norm().item())
        
        print(f"Batch {i+1}: VAN feat grad = {van_feat_grad.norm().item():.6f}, "
              f"U-Net feat grad = {unet_feat_grad.norm().item():.6f}")
    
    return results


def visualize_comprehensive_analysis(results, save_dir='gradient_analysis'):
    """
    全面可视化分析结果
    """
    os.makedirs(save_dir, exist_ok=True)
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 参数梯度对比
    axes[0, 0].plot(results['param_gradients']['van'], 'b-', label='VAN Branch', linewidth=2, marker='o')
    axes[0, 0].plot(results['param_gradients']['unet'], 'r-', label='U-Net Branch', linewidth=2, marker='s')
    axes[0, 0].set_title('Parameter Gradient Comparison')
    axes[0, 0].set_xlabel('Batch')
    axes[0, 0].set_ylabel('Parameter Gradient Norm')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 特征梯度对比
    axes[0, 1].plot(results['feature_gradients']['van'], 'b-', label='VAN Features', linewidth=2, marker='o')
    axes[0, 1].plot(results['feature_gradients']['unet'], 'r-', label='U-Net Features', linewidth=2, marker='s')
    axes[0, 1].set_title('Feature Gradient Comparison')
    axes[0, 1].set_xlabel('Batch')
    axes[0, 1].set_ylabel('Feature Gradient Norm')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 特征大小对比
    axes[0, 2].plot(results['feature_magnitudes']['van'], 'b-', label='VAN Features', linewidth=2, marker='o')
    axes[0, 2].plot(results['feature_magnitudes']['unet'], 'r-', label='U-Net Features', linewidth=2, marker='s')
    axes[0, 2].set_title('Feature Magnitude Comparison')
    axes[0, 2].set_xlabel('Batch')
    axes[0, 2].set_ylabel('Feature Norm')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 损失对比
    axes[1, 0].plot(results['losses']['van'], 'b-', label='VAN Only', linewidth=2, marker='o')
    axes[1, 0].plot(results['losses']['unet'], 'r-', label='U-Net Only', linewidth=2, marker='s')
    axes[1, 0].plot(results['losses']['fusion'], 'g-', label='Fusion', linewidth=2, marker='^')
    axes[1, 0].set_title('Loss Comparison')
    axes[1, 0].set_xlabel('Batch')
    axes[1, 0].set_ylabel('MSE Loss')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].set_yscale('log')
    
    # 5. 梯度比率
    param_ratios = [v/u if u > 0 else 0 for v, u in zip(results['param_gradients']['van'], results['param_gradients']['unet'])]
    feat_ratios = [v/u if u > 0 else 0 for v, u in zip(results['feature_gradients']['van'], results['feature_gradients']['unet'])]
    
    axes[1, 1].plot(param_ratios, 'g-', label='Parameter Ratio', linewidth=2, marker='o')
    axes[1, 1].plot(feat_ratios, 'm-', label='Feature Ratio', linewidth=2, marker='s')
    axes[1, 1].axhline(y=1, color='k', linestyle='--', alpha=0.5, label='Equal')
    axes[1, 1].set_title('VAN/U-Net Gradient Ratios')
    axes[1, 1].set_xlabel('Batch')
    axes[1, 1].set_ylabel('Ratio')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 统计汇总
    stats = {
        'Param Grad VAN': np.mean(results['param_gradients']['van']),
        'Param Grad U-Net': np.mean(results['param_gradients']['unet']),
        'Feat Grad VAN': np.mean(results['feature_gradients']['van']),
        'Feat Grad U-Net': np.mean(results['feature_gradients']['unet']),
        'Feat Mag VAN': np.mean(results['feature_magnitudes']['van']),
        'Feat Mag U-Net': np.mean(results['feature_magnitudes']['unet'])
    }
    
    categories = list(stats.keys())
    values = list(stats.values())
    colors = ['blue', 'red', 'blue', 'red', 'blue', 'red']

    bars = axes[1, 2].bar(range(len(categories)), values, color=colors, alpha=0.7)
    axes[1, 2].set_title('Average Statistics')
    axes[1, 2].set_ylabel('Value')
    axes[1, 2].set_xticks(range(len(categories)))
    axes[1, 2].set_xticklabels(categories, rotation=45, ha='right')
    axes[1, 2].set_yscale('log')
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        axes[1, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height(),
                       f'{value:.4f}', ha='center', va='bottom', fontsize=8)
    
    plt.suptitle('Comprehensive Gradient Analysis - VCBNet', fontsize=16)
    plt.tight_layout()
    
    save_path = os.path.join(save_dir, 'comprehensive_gradient_analysis.png')
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"Comprehensive analysis saved: {save_path}")
    
    # 打印详细统计
    print(f"\n=== Comprehensive Analysis Results ===")
    print(f"Parameter Gradients:")
    print(f"  VAN: {np.mean(results['param_gradients']['van']):.6f} ± {np.std(results['param_gradients']['van']):.6f}")
    print(f"  U-Net: {np.mean(results['param_gradients']['unet']):.6f} ± {np.std(results['param_gradients']['unet']):.6f}")
    print(f"  Ratio: {np.mean(results['param_gradients']['van'])/np.mean(results['param_gradients']['unet']):.4f}")
    
    print(f"\nFeature Gradients:")
    print(f"  VAN: {np.mean(results['feature_gradients']['van']):.6f} ± {np.std(results['feature_gradients']['van']):.6f}")
    print(f"  U-Net: {np.mean(results['feature_gradients']['unet']):.6f} ± {np.std(results['feature_gradients']['unet']):.6f}")
    print(f"  Ratio: {np.mean(results['feature_gradients']['van'])/np.mean(results['feature_gradients']['unet']):.4f}")
    
    print(f"\nFeature Magnitudes:")
    print(f"  VAN: {np.mean(results['feature_magnitudes']['van']):.6f} ± {np.std(results['feature_magnitudes']['van']):.6f}")
    print(f"  U-Net: {np.mean(results['feature_magnitudes']['unet']):.6f} ± {np.std(results['feature_magnitudes']['unet']):.6f}")
    print(f"  Ratio: {np.mean(results['feature_magnitudes']['van'])/np.mean(results['feature_magnitudes']['unet']):.4f}")
    
    print(f"\nAverage Losses:")
    print(f"  VAN Only: {np.mean(results['losses']['van']):.6f}")
    print(f"  U-Net Only: {np.mean(results['losses']['unet']):.6f}")
    print(f"  Fusion: {np.mean(results['losses']['fusion']):.6f}")


def main():
    # 配置
    model_dir = "721/vcbnet0"
    data_path = "data/test/202210090536_match.npy"
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    print("=== Comprehensive Gradient Analysis ===")
    
    # 加载模型
    config_path = os.path.join(model_dir, 'model_param.json')
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    model = VCBNet(n_channels=config['in_shape'][0], n_outputs=config['out_chans'])
    model = model.to(device)
    
    # 加载权重
    checkpoint_path = os.path.join(model_dir, 'checkpoints', 'checkpoint.pth')
    checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
    state_dict = checkpoint['model']
    
    new_state_dict = {}
    for key, value in state_dict.items():
        if key.startswith('_orig_mod.'):
            new_key = key[10:]
        else:
            new_key = key
        new_state_dict[new_key] = value
    
    model.load_state_dict(new_state_dict, strict=False)
    print("Model loaded successfully")
    
    # 创建数据加载器
    img_size = (config['in_shape'][1], config['in_shape'][2])
    dataset = RadarSets(data_path, img_size, mode='test', data_type='vis')
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # 分析梯度
    print("\nAnalyzing gradients...")
    results = analyze_gradients_correctly(model, dataloader, device)
    
    # 可视化结果
    print("\nGenerating visualizations...")
    visualize_comprehensive_analysis(results)
    
    print("\nAnalysis completed!")


if __name__ == "__main__":
    main()
