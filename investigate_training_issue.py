#!/usr/bin/env python3
"""
调查双分支训练中的问题
分析为什么单独训练VAN效果好，但双分支训练中VAN激活变小
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from model import VCBNet
from API.dataloader import RadarSets
from torch.utils.data import DataLoader


class VCBNetInvestigator(VCBNet):
    """
    继承VCBNet，用于调查训练问题
    """
    def forward(self, x, return_all=False):
        # VAN分支
        xx = self.backbone(x)
        stage1, stage2, stage3, stage4 = xx

        up4 = self.up4(stage4)
        up4 = torch.cat([up4, stage3], dim=1)
        up4 = self.up4_(up4)

        up3 = self.up3(up4)
        up3 = torch.cat([up3, stage2], dim=1)
        up3 = self.up3_(up3)

        up2 = self.up2(up3)
        up2 = torch.cat([up2, stage1], dim=1)
        up2 = self.up2_(up2)

        van_feat = self.up1(up2)  # VAN最终特征
        out2 = self.cls_reg(van_feat)  # VAN输出

        # U-Net分支
        x1_1 = self.conv(x)
        x1_2 = self.down1_1(x1_1)
        x1_3 = self.down1_2(x1_2)
        x1_4 = self.down1_3(x1_3)
        x1_5 = self.down1_4(x1_4)
        x1_6 = self.up1_1(x1_5, x1_4)
        x1_7 = self.up1_2(x1_6, x1_3)
        x1_8 = self.up1_3(x1_7, x1_2)
        unet_feat = self.up1_4(x1_8, x1_1)  # U-Net最终特征
        out1 = self.out(unet_feat)  # U-Net输出

        # 融合
        final_out = out1 * 0.5 + out2 * 0.5

        if return_all:
            # 确保特征可以计算梯度
            if van_feat.requires_grad:
                van_feat.retain_grad()
            if unet_feat.requires_grad:
                unet_feat.retain_grad()

            return {
                'final_output': final_out,
                'van_output': out2,
                'unet_output': out1,
                'van_features': van_feat,
                'unet_features': unet_feat,
                'van_backbone_features': [stage1, stage2, stage3, stage4]
            }
        else:
            return final_out


def analyze_gradient_flow(model, data_loader, device):
    """
    分析梯度流向，包括参数梯度和特征梯度
    """
    model.train()
    criterion = torch.nn.MSELoss()

    # 收集梯度信息
    param_gradients = {'van': [], 'unet': []}
    feature_gradients = {'van': [], 'unet': []}

    for i, (inputs, targets) in enumerate(data_loader):
        if i >= 5:  # 只分析前5个batch
            break

        inputs, targets = inputs.to(device), targets.to(device)

        # 前向传播 - 确保特征可以计算梯度
        outputs = model(inputs, return_all=True)

        # 计算损失
        loss = criterion(outputs['final_output'], targets)

        # 反向传播
        model.zero_grad()
        loss.backward(retain_graph=True)

        # 1. 收集参数梯度 (原来的方法)
        van_param_grad = 0
        for name, param in model.named_parameters():
            if 'backbone' in name or 'up4' in name or 'up3' in name or 'up2' in name or 'up1' in name or 'cls_reg' in name:
                if param.grad is not None:
                    van_param_grad += param.grad.norm().item() ** 2
        param_gradients['van'].append(np.sqrt(van_param_grad))

        unet_param_grad = 0
        for name, param in model.named_parameters():
            if 'conv' in name or 'down1' in name or 'up1_' in name or 'out' in name:
                if param.grad is not None:
                    unet_param_grad += param.grad.norm().item() ** 2
        param_gradients['unet'].append(np.sqrt(unet_param_grad))

        # 2. 收集特征梯度 (新增)
        # 检查特征是否有梯度
        if outputs['van_features'].grad is not None:
            feature_gradients['van'].append(outputs['van_features'].grad.norm().item())
        else:
            feature_gradients['van'].append(0.0)

        if outputs['unet_features'].grad is not None:
            feature_gradients['unet'].append(outputs['unet_features'].grad.norm().item())
        else:
            feature_gradients['unet'].append(0.0)

    return param_gradients, feature_gradients


def analyze_feature_contribution(model, data_loader, device):
    """
    分析特征贡献度
    """
    model.eval()
    
    contributions = {
        'van_only': [],
        'unet_only': [],
        'fusion': [],
        'ground_truth': []
    }
    
    with torch.no_grad():
        for i, (inputs, targets) in enumerate(data_loader):
            if i >= 10:  # 分析前10个样本
                break
                
            inputs, targets = inputs.to(device), targets.to(device)
            
            # 获取所有输出
            outputs = model(inputs, return_all=True)
            
            # 计算与真值的MSE
            van_mse = torch.nn.functional.mse_loss(outputs['van_output'], targets).item()
            unet_mse = torch.nn.functional.mse_loss(outputs['unet_output'], targets).item()
            fusion_mse = torch.nn.functional.mse_loss(outputs['final_output'], targets).item()
            
            contributions['van_only'].append(van_mse)
            contributions['unet_only'].append(unet_mse)
            contributions['fusion'].append(fusion_mse)
            contributions['ground_truth'].append(0)  # 参考线
    
    return contributions


def visualize_investigation(param_gradients, feature_gradients, contributions, save_dir='investigation_results'):
    """
    可视化调查结果
    """
    os.makedirs(save_dir, exist_ok=True)

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # 1. 参数梯度对比
    axes[0, 0].plot(param_gradients['van'], 'b-', label='VAN Branch', linewidth=2)
    axes[0, 0].plot(param_gradients['unet'], 'r-', label='U-Net Branch', linewidth=2)
    axes[0, 0].set_title('Parameter Gradient Comparison')
    axes[0, 0].set_xlabel('Batch')
    axes[0, 0].set_ylabel('Parameter Gradient Norm')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 特征梯度对比
    axes[0, 1].plot(feature_gradients['van'], 'b-', label='VAN Features', linewidth=2)
    axes[0, 1].plot(feature_gradients['unet'], 'r-', label='U-Net Features', linewidth=2)
    axes[0, 1].set_title('Feature Gradient Comparison')
    axes[0, 1].set_xlabel('Batch')
    axes[0, 1].set_ylabel('Feature Gradient Norm')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 梯度比率对比
    param_ratios = [v/u if u > 0 else 0 for v, u in zip(param_gradients['van'], param_gradients['unet'])]
    feature_ratios = [v/u if u > 0 else 0 for v, u in zip(feature_gradients['van'], feature_gradients['unet'])]

    axes[0, 2].plot(param_ratios, 'g-', label='Parameter Ratio', linewidth=2)
    axes[0, 2].plot(feature_ratios, 'm-', label='Feature Ratio', linewidth=2)
    axes[0, 2].axhline(y=1, color='k', linestyle='--', alpha=0.5, label='Equal')
    axes[0, 2].set_title('VAN/U-Net Gradient Ratios')
    axes[0, 2].set_xlabel('Batch')
    axes[0, 2].set_ylabel('Ratio')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 3. MSE对比
    x_pos = range(len(contributions['van_only']))
    axes[1, 0].plot(x_pos, contributions['van_only'], 'b-', label='VAN Only', linewidth=2)
    axes[1, 0].plot(x_pos, contributions['unet_only'], 'r-', label='U-Net Only', linewidth=2)
    axes[1, 0].plot(x_pos, contributions['fusion'], 'g-', label='Fusion', linewidth=2)
    axes[1, 0].set_title('MSE Comparison by Sample')
    axes[1, 0].set_xlabel('Sample')
    axes[1, 0].set_ylabel('MSE')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].set_yscale('log')
    
    # 4. 平均性能对比
    avg_van = np.mean(contributions['van_only'])
    avg_unet = np.mean(contributions['unet_only'])
    avg_fusion = np.mean(contributions['fusion'])
    
    methods = ['VAN Only', 'U-Net Only', 'Fusion']
    mse_values = [avg_van, avg_unet, avg_fusion]
    colors = ['blue', 'red', 'green']
    
    bars = axes[1, 1].bar(methods, mse_values, color=colors, alpha=0.7)
    axes[1, 1].set_title('Average MSE Comparison')
    axes[1, 1].set_ylabel('Average MSE')
    axes[1, 1].set_yscale('log')
    
    # 添加数值标签
    for bar, value in zip(bars, mse_values):
        axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height(),
                       f'{value:.4f}', ha='center', va='bottom')
    
    plt.suptitle('VCBNet Training Issue Investigation', fontsize=16)
    plt.tight_layout()
    
    save_path = os.path.join(save_dir, 'training_investigation.png')
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"Investigation results saved: {save_path}")
    
    # 打印统计结果
    print(f"\n=== Investigation Results ===")
    print(f"Parameter Gradients:")
    print(f"  Average VAN: {np.mean(param_gradients['van']):.4f}")
    print(f"  Average U-Net: {np.mean(param_gradients['unet']):.4f}")
    print(f"  Ratio (VAN/U-Net): {np.mean(param_gradients['van'])/np.mean(param_gradients['unet']):.4f}")

    print(f"\nFeature Gradients:")
    print(f"  Average VAN: {np.mean(feature_gradients['van']):.4f}")
    print(f"  Average U-Net: {np.mean(feature_gradients['unet']):.4f}")
    print(f"  Ratio (VAN/U-Net): {np.mean(feature_gradients['van'])/np.mean(feature_gradients['unet']):.4f}")

    print(f"\nAverage MSE:")
    print(f"  VAN only: {avg_van:.6f}")
    print(f"  U-Net only: {avg_unet:.6f}")
    print(f"  Fusion: {avg_fusion:.6f}")
    print(f"\nPerformance ranking:")
    performance = [('VAN Only', avg_van), ('U-Net Only', avg_unet), ('Fusion', avg_fusion)]
    performance.sort(key=lambda x: x[1])
    for i, (method, mse) in enumerate(performance, 1):
        print(f"  {i}. {method}: {mse:.6f}")


def main():
    # 配置
    model_dir = "721/vcbnet0"
    data_path = "data/test/202210090536_match.npy"
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    print("=== VCBNet Training Issue Investigation ===")
    
    # 加载模型
    config_path = os.path.join(model_dir, 'model_param.json')
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    model = VCBNetInvestigator(n_channels=config['in_shape'][0], n_outputs=config['out_chans'])
    model = model.to(device)
    
    # 加载权重
    checkpoint_path = os.path.join(model_dir, 'checkpoints', 'checkpoint.pth')
    checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
    state_dict = checkpoint['model']
    
    new_state_dict = {}
    for key, value in state_dict.items():
        if key.startswith('_orig_mod.'):
            new_key = key[10:]
        else:
            new_key = key
        new_state_dict[new_key] = value
    
    model.load_state_dict(new_state_dict, strict=False)
    print("Model loaded successfully")
    
    # 创建数据加载器
    img_size = (config['in_shape'][1], config['in_shape'][2])
    dataset = RadarSets(data_path, img_size, mode='test', data_type='vis')
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # 分析梯度流
    print("\n1. Analyzing gradient flow...")
    param_gradients, feature_gradients = analyze_gradient_flow(model, dataloader, device)

    # 分析特征贡献
    print("2. Analyzing feature contributions...")
    contributions = analyze_feature_contribution(model, dataloader, device)

    # 可视化结果
    print("3. Generating visualizations...")
    visualize_investigation(param_gradients, feature_gradients, contributions)
    
    print("\nInvestigation completed!")


if __name__ == "__main__":
    main()
