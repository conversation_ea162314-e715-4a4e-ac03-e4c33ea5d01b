#!/usr/bin/env python3
"""
VCBNet with Layer Normalization
最小化修改的归一化版本：
1. 在解码器关键位置添加Layer Normalization
2. 最终特征使用ReLU后再融合
3. 保持原有的0.5:0.5融合权重
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from .vcbnet import VCBNet, RegressionOutConv, DoubleConv, Down, Up
from .vcbnet import van_base


class VCBNetLN(VCBNet):
    """
    带Layer Normalization的VCBNet
    最小化修改，只在关键位置添加归一化
    """
    def __init__(self, n_channels=3, n_outputs=1):
        super().__init__(n_channels, n_outputs)
        
        # 在解码器的关键位置添加Layer Normalization
        # 只在最终特征层添加，保持简单
        self.van_final_ln = nn.LayerNorm([32, 416, 448])  # [C, H, W]
        self.unet_final_ln = nn.LayerNorm([32, 416, 448])  # [C, H, W]
        
        print("Initialized VCBNetLN with Layer Normalization on final features")
    
    def forward(self, x):
        # VAN分支 (ConvNeXt骨干网络) - 保持原有逻辑
        xx = self.backbone(x)
        stage1, stage2, stage3, stage4 = xx
        
        up4 = self.up4(stage4)
        up4 = torch.cat([up4, stage3], dim=1)
        up4 = self.up4_(up4)
        
        up3 = self.up3(up4)
        up3 = torch.cat([up3, stage2], dim=1)
        up3 = self.up3_(up3)
        
        up2 = self.up2(up3)
        up2 = torch.cat([up2, stage1], dim=1)
        up2 = self.up2_(up2)
        
        van_feat = self.up1(up2)  # [B, 32, H, W]
        
        # U-Net分支 - 保持原有逻辑
        x1_1 = self.conv(x)
        x1_2 = self.down1_1(x1_1)
        x1_3 = self.down1_2(x1_2)
        x1_4 = self.down1_3(x1_3)
        x1_5 = self.down1_4(x1_4)
        x1_6 = self.up1_1(x1_5, x1_4)
        x1_7 = self.up1_2(x1_6, x1_3)
        x1_8 = self.up1_3(x1_7, x1_2)
        unet_feat = self.up1_4(x1_8, x1_1)  # [B, 32, H, W]
        
        # 关键修改1: 对最终特征进行Layer Normalization
        van_feat_ln = self.van_final_ln(van_feat)
        unet_feat_ln = self.unet_final_ln(unet_feat)
        
        # 关键修改2: 使用ReLU激活后再计算输出
        van_feat_relu = F.relu(van_feat_ln)
        unet_feat_relu = F.relu(unet_feat_ln)
        
        # 计算各分支输出
        out2 = self.cls_reg(van_feat_relu)   # VAN分支输出
        out1 = self.out(unet_feat_relu)      # U-Net分支输出
        
        # 关键修改3: 输出也使用ReLU确保非负
        out1 = F.relu(out1)
        out2 = F.relu(out2)
        
        # 保持原有的0.5:0.5融合
        out = out1 * 0.5 + out2 * 0.5
        
        return out


class VCBNetLNAdvanced(VCBNet):
    """
    稍微高级一点的版本，在更多层添加归一化
    如果基础版本效果好，可以尝试这个版本
    """
    def __init__(self, n_channels=3, n_outputs=1):
        super().__init__(n_channels, n_outputs)
        
        # 在解码器的多个位置添加Layer Normalization
        # VAN分支归一化
        self.van_ln_up4 = nn.LayerNorm([256, 52, 56])   # 根据实际尺寸调整
        self.van_ln_up3 = nn.LayerNorm([128, 104, 112])
        self.van_ln_up2 = nn.LayerNorm([64, 208, 224])
        self.van_ln_final = nn.LayerNorm([32, 416, 448])
        
        # U-Net分支归一化
        self.unet_ln_up1 = nn.LayerNorm([256, 52, 56])
        self.unet_ln_up2 = nn.LayerNorm([128, 104, 112])
        self.unet_ln_up3 = nn.LayerNorm([64, 208, 224])
        self.unet_ln_final = nn.LayerNorm([32, 416, 448])
        
        print("Initialized VCBNetLNAdvanced with multi-layer normalization")
    
    def forward(self, x):
        # VAN分支
        xx = self.backbone(x)
        stage1, stage2, stage3, stage4 = xx
        
        up4 = self.up4(stage4)
        up4 = torch.cat([up4, stage3], dim=1)
        up4 = self.up4_(up4)
        up4 = self.van_ln_up4(up4)  # 添加归一化
        
        up3 = self.up3(up4)
        up3 = torch.cat([up3, stage2], dim=1)
        up3 = self.up3_(up3)
        up3 = self.van_ln_up3(up3)  # 添加归一化
        
        up2 = self.up2(up3)
        up2 = torch.cat([up2, stage1], dim=1)
        up2 = self.up2_(up2)
        up2 = self.van_ln_up2(up2)  # 添加归一化
        
        van_feat = self.up1(up2)
        van_feat = self.van_ln_final(van_feat)  # 最终归一化
        
        # U-Net分支
        x1_1 = self.conv(x)
        x1_2 = self.down1_1(x1_1)
        x1_3 = self.down1_2(x1_2)
        x1_4 = self.down1_3(x1_3)
        x1_5 = self.down1_4(x1_4)
        
        x1_6 = self.up1_1(x1_5, x1_4)
        x1_6 = self.unet_ln_up1(x1_6)  # 添加归一化
        
        x1_7 = self.up1_2(x1_6, x1_3)
        x1_7 = self.unet_ln_up2(x1_7)  # 添加归一化
        
        x1_8 = self.up1_3(x1_7, x1_2)
        x1_8 = self.unet_ln_up3(x1_8)  # 添加归一化
        
        unet_feat = self.up1_4(x1_8, x1_1)
        unet_feat = self.unet_ln_final(unet_feat)  # 最终归一化
        
        # 使用ReLU激活
        van_feat_relu = F.relu(van_feat)
        unet_feat_relu = F.relu(unet_feat)
        
        # 计算输出
        out2 = self.cls_reg(van_feat_relu)
        out1 = self.out(unet_feat_relu)
        
        # 输出ReLU
        out1 = F.relu(out1)
        out2 = F.relu(out2)
        
        # 0.5:0.5融合
        out = out1 * 0.5 + out2 * 0.5
        
        return out


def create_vcbnet_ln(version='basic', **kwargs):
    """
    工厂函数，创建不同版本的Layer Norm VCBNet
    
    Args:
        version: 'basic' 或 'advanced'
    """
    if version == 'basic':
        return VCBNetLN(**kwargs)
    elif version == 'advanced':
        return VCBNetLNAdvanced(**kwargs)
    else:
        raise ValueError(f"Unknown version: {version}")


if __name__ == '__main__':
    print("Testing VCBNetLN...")
    
    # 测试基础版本
    model_basic = create_vcbnet_ln('basic', n_channels=3, n_outputs=1)
    
    # 测试输入
    x = torch.randn(1, 3, 416, 448)
    
    print("\n=== Basic VCBNetLN ===")
    with torch.no_grad():
        output = model_basic(x)
        print(f"Input shape: {x.shape}")
        print(f"Output shape: {output.shape}")
        print(f"Output range: {output.min():.6f} ~ {output.max():.6f}")
        print(f"Output mean: {output.mean():.6f}")
        
        # 检查是否有负值
        negative_count = (output < 0).sum().item()
        total_count = output.numel()
        print(f"Negative values: {negative_count}/{total_count} ({negative_count/total_count*100:.2f}%)")
        
        if negative_count == 0:
            print("✅ All outputs are non-negative!")
        else:
            print("❌ Still has negative values")
    
    print("\n=== Model Summary ===")
    print(f"Total parameters: {sum(p.numel() for p in model_basic.parameters()):,}")
    print(f"Trainable parameters: {sum(p.numel() for p in model_basic.parameters() if p.requires_grad):,}")
    
    print("\n✅ VCBNetLN test completed!")
