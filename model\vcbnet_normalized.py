#!/usr/bin/env python3
"""
VCBNet with Feature Normalization
解决两个分支解码器特征值范围差距大的问题，使融合更加有效
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from .vcbnet import VCBNet, RegressionOutConv, DoubleConv, Down, Up
from .vcbnet import van_base


class FeatureNormalizer(nn.Module):
    """
    特征归一化模块，用于统一不同分支的特征范围
    """
    def __init__(self, num_features, norm_type='layer'):
        super().__init__()
        self.norm_type = norm_type
        self.num_features = num_features
        
        if norm_type == 'adaptive':
            # 可学习的归一化参数
            self.weight = nn.Parameter(torch.ones(num_features))
            self.bias = nn.Parameter(torch.zeros(num_features))
        elif norm_type == 'batch':
            self.bn = nn.BatchNorm2d(num_features)
        elif norm_type == 'instance':
            self.in_norm = nn.InstanceNorm2d(num_features)
    
    def forward(self, x):
        if self.norm_type == 'layer':
            # Layer normalization - 对空间和通道维度归一化
            return F.layer_norm(x, x.shape[1:])
        
        elif self.norm_type == 'adaptive':
            # 自适应归一化
            mean = x.mean(dim=[2, 3], keepdim=True)
            std = x.std(dim=[2, 3], keepdim=True)
            x_norm = (x - mean) / (std + 1e-8)
            return x_norm * self.weight.view(1, -1, 1, 1) + self.bias.view(1, -1, 1, 1)
        
        elif self.norm_type == 'batch':
            return self.bn(x)
        
        elif self.norm_type == 'instance':
            return self.in_norm(x)
        
        elif self.norm_type == 'l2':
            # L2归一化
            return F.normalize(x, p=2, dim=1)
        
        elif self.norm_type == 'minmax':
            # Min-Max归一化
            batch_size = x.size(0)
            x_flat = x.view(batch_size, -1)
            x_min = x_flat.min(dim=1, keepdim=True)[0]
            x_max = x_flat.max(dim=1, keepdim=True)[0]
            x_norm = (x_flat - x_min) / (x_max - x_min + 1e-8)
            return x_norm.view_as(x)
        
        else:
            return x


class NormalizedVCBNet(VCBNet):
    """
    带特征归一化的VCBNet
    在解码器的关键位置添加归一化，使两个分支的特征范围接近
    """
    def __init__(self, n_channels=3, n_outputs=1, norm_type='adaptive', fusion_weight=0.7):
        super().__init__(n_channels, n_outputs)
        
        self.norm_type = norm_type
        self.fusion_weight = fusion_weight  # VAN分支的权重
        
        # 为解码器的每一层添加归一化
        # VAN分支归一化
        self.van_norm_up4 = FeatureNormalizer(256, norm_type)
        self.van_norm_up3 = FeatureNormalizer(128, norm_type)
        self.van_norm_up2 = FeatureNormalizer(64, norm_type)
        self.van_norm_final = FeatureNormalizer(32, norm_type)
        
        # U-Net分支归一化
        self.unet_norm_up1 = FeatureNormalizer(256, norm_type)
        self.unet_norm_up2 = FeatureNormalizer(128, norm_type)
        self.unet_norm_up3 = FeatureNormalizer(64, norm_type)
        self.unet_norm_final = FeatureNormalizer(32, norm_type)
        
        # 融合前的最终归一化
        self.pre_fusion_norm_van = FeatureNormalizer(32, norm_type)
        self.pre_fusion_norm_unet = FeatureNormalizer(32, norm_type)
        
        print(f"Initialized NormalizedVCBNet with {norm_type} normalization")
        print(f"Fusion weight: VAN={fusion_weight:.1f}, U-Net={1-fusion_weight:.1f}")
    
    def forward(self, x, return_features=False):
        # VAN分支 (ConvNeXt骨干网络)
        xx = self.backbone(x)
        stage1, stage2, stage3, stage4 = xx
        
        # VAN解码器 - 添加归一化
        up4 = self.up4(stage4)
        up4 = torch.cat([up4, stage3], dim=1)
        up4 = self.up4_(up4)
        up4 = self.van_norm_up4(up4)  # 归一化
        
        up3 = self.up3(up4)
        up3 = torch.cat([up3, stage2], dim=1)
        up3 = self.up3_(up3)
        up3 = self.van_norm_up3(up3)  # 归一化
        
        up2 = self.up2(up3)
        up2 = torch.cat([up2, stage1], dim=1)
        up2 = self.up2_(up2)
        up2 = self.van_norm_up2(up2)  # 归一化
        
        van_feat = self.up1(up2)
        van_feat = self.van_norm_final(van_feat)  # 归一化
        
        # U-Net分支 - 添加归一化
        x1_1 = self.conv(x)
        x1_2 = self.down1_1(x1_1)
        x1_3 = self.down1_2(x1_2)
        x1_4 = self.down1_3(x1_3)
        x1_5 = self.down1_4(x1_4)
        
        x1_6 = self.up1_1(x1_5, x1_4)
        x1_6 = self.unet_norm_up1(x1_6)  # 归一化
        
        x1_7 = self.up1_2(x1_6, x1_3)
        x1_7 = self.unet_norm_up2(x1_7)  # 归一化
        
        x1_8 = self.up1_3(x1_7, x1_2)
        x1_8 = self.unet_norm_up3(x1_8)  # 归一化
        
        unet_feat = self.up1_4(x1_8, x1_1)
        unet_feat = self.unet_norm_final(unet_feat)  # 归一化
        
        # 融合前的最终归一化 - 确保两个分支在同一尺度
        van_feat_norm = self.pre_fusion_norm_van(van_feat)
        unet_feat_norm = self.pre_fusion_norm_unet(unet_feat)
        
        # 计算输出
        out2 = self.cls_reg(van_feat_norm)  # VAN分支输出
        out1 = self.out(unet_feat_norm)     # U-Net分支输出
        
        # 添加ReLU确保非负
        out1 = F.relu(out1)
        out2 = F.relu(out2)
        
        # 融合两个分支的输出 - 使用调整后的权重
        out = out1 * (1 - self.fusion_weight) + out2 * self.fusion_weight
        
        if return_features:
            return {
                'output': out,
                'van_output': out2,
                'unet_output': out1,
                'van_features': van_feat_norm,
                'unet_features': unet_feat_norm,
                'van_features_raw': van_feat,
                'unet_features_raw': unet_feat
            }
        
        return out


class AdaptiveFusionVCBNet(NormalizedVCBNet):
    """
    带自适应融合的归一化VCBNet
    不仅归一化特征，还学习最优的融合权重
    """
    def __init__(self, n_channels=3, n_outputs=1, norm_type='adaptive'):
        super().__init__(n_channels, n_outputs, norm_type, fusion_weight=0.5)
        
        # 自适应融合模块
        self.fusion_attention = nn.Sequential(
            nn.Conv2d(64, 32, kernel_size=3, padding=1),  # 64 = 32 + 32 (两个分支特征拼接)
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 2, kernel_size=1),  # 输出2个权重
            nn.Softmax(dim=1)  # 确保权重和为1
        )
        
        print("Initialized AdaptiveFusionVCBNet with learnable fusion weights")
    
    def forward(self, x, return_features=False):
        # 获取归一化后的特征
        results = super().forward(x, return_features=True)
        
        van_feat_norm = results['van_features']
        unet_feat_norm = results['unet_features']
        
        # 计算自适应融合权重
        combined_feat = torch.cat([van_feat_norm, unet_feat_norm], dim=1)  # [B, 64, H, W]
        fusion_weights = self.fusion_attention(combined_feat)  # [B, 2, H, W]
        
        # 分离权重
        van_weight = fusion_weights[:, 0:1, :, :]   # [B, 1, H, W]
        unet_weight = fusion_weights[:, 1:2, :, :]  # [B, 1, H, W]
        
        # 计算输出
        out2 = self.cls_reg(van_feat_norm)  # VAN分支输出
        out1 = self.out(unet_feat_norm)     # U-Net分支输出
        
        # 添加ReLU确保非负
        out1 = F.relu(out1)
        out2 = F.relu(out2)
        
        # 空间自适应融合
        out = out1 * unet_weight + out2 * van_weight
        
        if return_features:
            results.update({
                'output': out,
                'van_output': out2,
                'unet_output': out1,
                'fusion_weights': fusion_weights,
                'van_weight': van_weight,
                'unet_weight': unet_weight
            })
            return results
        
        return out


def create_normalized_vcbnet(model_type='basic', norm_type='adaptive', **kwargs):
    """
    工厂函数，创建不同类型的归一化VCBNet
    
    Args:
        model_type: 'basic' 或 'adaptive'
        norm_type: 'layer', 'adaptive', 'batch', 'instance', 'l2', 'minmax'
    """
    if model_type == 'basic':
        return NormalizedVCBNet(norm_type=norm_type, **kwargs)
    elif model_type == 'adaptive':
        return AdaptiveFusionVCBNet(norm_type=norm_type, **kwargs)
    else:
        raise ValueError(f"Unknown model_type: {model_type}")


if __name__ == '__main__':
    # 测试不同的归一化方法
    print("Testing NormalizedVCBNet...")
    
    # 基础归一化模型
    model_basic = create_normalized_vcbnet('basic', norm_type='adaptive', fusion_weight=0.7)
    
    # 自适应融合模型
    model_adaptive = create_normalized_vcbnet('adaptive', norm_type='adaptive')
    
    # 测试输入
    x = torch.randn(1, 3, 416, 448)
    
    print("\n=== Basic Normalized Model ===")
    with torch.no_grad():
        results_basic = model_basic(x, return_features=True)
        print(f"VAN features range: {results_basic['van_features'].min():.4f} ~ {results_basic['van_features'].max():.4f}")
        print(f"U-Net features range: {results_basic['unet_features'].min():.4f} ~ {results_basic['unet_features'].max():.4f}")
        print(f"Output range: {results_basic['output'].min():.4f} ~ {results_basic['output'].max():.4f}")
    
    print("\n=== Adaptive Fusion Model ===")
    with torch.no_grad():
        results_adaptive = model_adaptive(x, return_features=True)
        print(f"VAN features range: {results_adaptive['van_features'].min():.4f} ~ {results_adaptive['van_features'].max():.4f}")
        print(f"U-Net features range: {results_adaptive['unet_features'].min():.4f} ~ {results_adaptive['unet_features'].max():.4f}")
        print(f"Fusion weights range: {results_adaptive['fusion_weights'].min():.4f} ~ {results_adaptive['fusion_weights'].max():.4f}")
        print(f"Output range: {results_adaptive['output'].min():.4f} ~ {results_adaptive['output'].max():.4f}")
