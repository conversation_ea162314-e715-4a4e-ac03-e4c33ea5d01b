#!/usr/bin/env python3
"""
简单的分支输出绘图脚本 - 完全按照simple_plot.py的方式
"""

import torch
import numpy as np
from model import VCBNet
import matplotlib.pyplot as plt

def analyze_and_plot():
    # 加载模型
    model = VCBNet(n_channels=3, n_outputs=1)
    checkpoint = torch.load('721/vcbnet0/checkpoints/checkpoint.pth', map_location='cpu')
    
    # 处理checkpoint格式
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint
    
    # 处理 _orig_mod. 前缀
    new_state_dict = {}
    for key, value in state_dict.items():
        if key.startswith('_orig_mod.'):
            new_key = key[10:]
            new_state_dict[new_key] = value
        else:
            new_state_dict[key] = value
    
    model.load_state_dict(new_state_dict, strict=False)
    model.eval()
    
    # 存储输出
    branch_outputs = {}
    
    def capture_output(name):
        def hook(module, input, output):
            branch_outputs[name] = output.detach().clone()
        return hook
    
    # 注册hooks
    model.out.register_forward_hook(capture_output('unet'))
    model.cls_reg.register_forward_hook(capture_output('van'))
    
    # 加载数据
    data = np.load('data/test/202210090536_match.npy')
    input_tensor = torch.from_numpy(data[:3]).unsqueeze(0).float()
    ground_truth = data[0]  # GT是第0个通道
    
    # 推理
    with torch.no_grad():
        final_out = model(input_tensor)
    
    # 获取分支输出
    unet_out = branch_outputs['unet']
    van_out = branch_outputs['van']
    
    # 转换为numpy并按simple_plot.py处理
    gt_np = ground_truth * 60
    gt_np[gt_np < 10] = np.nan
    
    final_np = final_out[0, 0].cpu().numpy() * 60
    final_np[final_np < 10] = np.nan
    
    unet_np = unet_out[0, 0].cpu().numpy() * 60
    unet_np[unet_np < 10] = np.nan
    
    van_np = van_out[0, 0].cpu().numpy() * 60
    # VAN不过滤，因为有负值
    
    # 绘图
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('VCBNet Branch Analysis', fontsize=14)
    
    # 颜色范围
    vmin, vmax = 10, 70
    
    # UNet分支
    im1 = axes[0, 0].imshow(unet_np, cmap='jet', vmin=vmin, vmax=vmax)
    axes[0, 0].set_title('UNet Branch')
    axes[0, 0].axis('off')
    
    # VAN分支
    im2 = axes[0, 1].imshow(van_np, cmap='RdBu_r')
    axes[0, 1].set_title('VAN Branch')
    axes[0, 1].axis('off')
    
    # 融合输出
    im3 = axes[1, 0].imshow(final_np, cmap='jet', vmin=vmin, vmax=vmax)
    axes[1, 0].set_title('Fused Output')
    axes[1, 0].axis('off')
    
    # 真值
    im4 = axes[1, 1].imshow(gt_np, cmap='jet', vmin=vmin, vmax=vmax)
    axes[1, 1].set_title('Ground Truth')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.savefig('branch_analysis_simple.png', dpi=200, bbox_inches='tight')
    print("📊 图片已保存到: branch_analysis_simple.png")
    plt.show()

if __name__ == "__main__":
    analyze_and_plot()
