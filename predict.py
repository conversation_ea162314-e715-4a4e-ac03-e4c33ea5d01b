#!/usr/bin/env python3
"""
预测脚本 - 加载训练好的模型进行预测
支持加载不同的模型类型和checkpoint文件
"""

import os
import json
import torch
import numpy as np
import argparse
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入模型
from model import VCBNet, VCBNet3
from API.dataloader import RadarSets
from torch.utils.data import DataLoader


def load_model_config(model_dir):
    """加载模型配置文件"""
    config_path = os.path.join(model_dir, 'model_param.json')
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    return config


def create_model(model_type, in_channels=3, out_channels=1):
    """根据模型类型创建模型"""
    if model_type == 'vcbnet':
        return VCBNet(n_channels=in_channels, n_outputs=out_channels)
    elif model_type == 'vcbnet3':
        return VCBNet3(n_channels=in_channels, n_outputs=out_channels)
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")


def load_checkpoint(model, checkpoint_path):
    """加载checkpoint文件"""
    print(f"正在加载checkpoint: {checkpoint_path}")

    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
    except Exception as e:
        raise RuntimeError(f"无法加载checkpoint文件: {e}")

    if 'model' not in checkpoint:
        raise KeyError("Checkpoint文件中没有找到'model'键")

    # 处理accelerator保存的模型（带有_orig_mod.前缀）
    state_dict = checkpoint['model']
    new_state_dict = {}

    for key, value in state_dict.items():
        # 移除_orig_mod.前缀
        if key.startswith('_orig_mod.'):
            new_key = key[10:]  # 移除'_orig_mod.'前缀
        else:
            new_key = key
        new_state_dict[new_key] = value

    # 获取当前模型的状态字典
    model_state_dict = model.state_dict()

    # 过滤掉不匹配的键
    filtered_state_dict = {}
    for key, value in new_state_dict.items():
        if key in model_state_dict:
            if model_state_dict[key].shape == value.shape:
                filtered_state_dict[key] = value
            else:
                print(f"跳过形状不匹配的参数: {key}, 模型形状: {model_state_dict[key].shape}, checkpoint形状: {value.shape}")
        else:
            print(f"跳过模型中不存在的参数: {key}")

    # 加载模型权重
    try:
        missing_keys, unexpected_keys = model.load_state_dict(filtered_state_dict, strict=False)

        # 详细的加载统计
        total_model_params = len(model_state_dict)
        loaded_params = len(filtered_state_dict)
        missing_params = len(missing_keys)

        print(f"\n=== 模型权重加载统计 ===")
        print(f"✅ 成功加载参数: {loaded_params}/{total_model_params} ({loaded_params/total_model_params:.1%})")

        if missing_params > 0:
            print(f"⚠️  未加载参数: {missing_params} 个")
            if missing_params <= 10:  # 如果缺失参数不多，显示具体名称
                print("   缺失的参数:")
                for key in missing_keys:
                    print(f"     - {key}")
            else:
                print(f"   缺失参数过多，显示前5个:")
                for key in missing_keys[:5]:
                    print(f"     - {key}")
                print(f"     ... 还有 {missing_params-5} 个")

        if len(unexpected_keys) > 0:
            print(f"⚠️  意外的参数: {len(unexpected_keys)} 个")
            if len(unexpected_keys) <= 5:
                for key in unexpected_keys:
                    print(f"     - {key}")

        # 判断加载是否完整
        if missing_params == 0:
            print("🎉 所有模型参数都已成功加载！")
        elif loaded_params / total_model_params >= 0.95:
            print("✅ 大部分参数已加载，模型应该可以正常工作")
        else:
            print("⚠️  加载的参数比例较低，请检查模型兼容性")

    except Exception as e:
        print(f"❌ 加载失败: {e}")
        raise e

    return model


def predict_single_file(model, data_path, device, data_type='vis', img_size=(416, 448)):
    """对单个文件进行预测"""
    # 创建数据集（单文件）
    dataset = RadarSets(data_path, img_size, mode='test', data_type=data_type)
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    model.eval()
    predictions = []
    
    with torch.no_grad():
        for inputs, _ in dataloader:  # 忽略targets，因为我们只需要预测
            inputs = inputs.to(device)

            # 模型预测
            outputs = model(inputs)

            # 转换为numpy数组
            pred_np = outputs.cpu().numpy()
            predictions.append(pred_np)
    
    return np.concatenate(predictions, axis=0) if predictions else None


def main():
    parser = argparse.ArgumentParser(description='模型预测脚本')
    parser.add_argument('--model_dir', type=str, default='721/vcbnet3_0', 
                       help='模型目录路径（包含checkpoints和model_param.json）')
    parser.add_argument('--test_data_dir', type=str, default='data/test',
                       help='测试数据目录')
    parser.add_argument('--output_dir', type=str, default='predictions',
                       help='预测结果保存目录')
    parser.add_argument('--device', type=str, default='auto',
                       help='设备选择: auto, cpu, cuda')
    parser.add_argument('--data_type', type=str, default='vis', choices=['vis', 'ir'],
                       help='数据类型')
    
    args = parser.parse_args()
    
    # 设备选择
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    print(f"使用设备: {device}")
    
    # 加载模型配置
    try:
        config = load_model_config(args.model_dir)
        print(f"加载配置成功: {config['model_type']}")
    except Exception as e:
        print(f"加载配置失败: {e}")
        return
    
    # 创建模型
    try:
        model = create_model(
            model_type=config['model_type'],
            in_channels=config['in_shape'][0],
            out_channels=config['out_chans']
        )
        model = model.to(device)
        print(f"模型创建成功: {config['model_type']}")
    except Exception as e:
        print(f"模型创建失败: {e}")
        return
    
    # 加载checkpoint
    checkpoint_path = os.path.join(args.model_dir, 'checkpoints', 'checkpoint.pth')
    try:
        model = load_checkpoint(model, checkpoint_path)
    except Exception as e:
        print(f"加载checkpoint失败: {e}")
        return
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 获取测试文件列表
    test_files = list(Path(args.test_data_dir).glob('*.npy'))
    if not test_files:
        print(f"在 {args.test_data_dir} 中没有找到.npy文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    # 图像尺寸
    img_size = (config['in_shape'][1], config['in_shape'][2])
    
    # 对每个文件进行预测
    for test_file in tqdm(test_files, desc="预测进度"):
        try:
            # 预测
            predictions = predict_single_file(
                model, str(test_file), device, 
                data_type=args.data_type, img_size=img_size
            )
            
            if predictions is not None:
                # 保存预测结果
                output_file = output_dir / f"{test_file.stem}_pred.npy"
                np.save(output_file, predictions)
                print(f"预测完成: {test_file.name} -> {output_file.name}")
            else:
                print(f"预测失败: {test_file.name}")
                
        except Exception as e:
            print(f"处理文件 {test_file.name} 时出错: {e}")
            continue
    
    print("所有预测完成！")


if __name__ == "__main__":
    main()
