#!/usr/bin/env python3
"""
测试归一化VCBNet的效果
对比原始模型和归一化模型的特征范围差异
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import os
from pathlib import Path

# 导入模型
from model.vcbnet import VCBNet
from model.vcbnet_normalized import create_normalized_vcbnet
from API.dataloader import RadarSets
from torch.utils.data import DataLoader


def analyze_feature_ranges(model, data_loader, device, model_name):
    """
    分析模型的特征范围
    """
    model.eval()
    
    van_features = []
    unet_features = []
    van_outputs = []
    unet_outputs = []
    final_outputs = []
    
    with torch.no_grad():
        for i, (inputs, targets) in enumerate(data_loader):
            if i >= 3:  # 只分析前3个样本
                break
                
            inputs = inputs.to(device)
            
            # 获取特征和输出
            if hasattr(model, 'forward') and 'return_features' in model.forward.__code__.co_varnames:
                results = model(inputs, return_features=True)
                van_feat = results['van_features']
                unet_feat = results['unet_features']
                van_out = results['van_output']
                unet_out = results['unet_output']
                final_out = results['output']
            else:
                # 原始模型，需要手动提取特征
                final_out = model(inputs)
                # 这里简化处理，只记录最终输出
                van_feat = torch.zeros(1, 32, 416, 448)
                unet_feat = torch.zeros(1, 32, 416, 448)
                van_out = final_out * 0.5  # 假设
                unet_out = final_out * 0.5  # 假设
            
            van_features.append(van_feat.cpu())
            unet_features.append(unet_feat.cpu())
            van_outputs.append(van_out.cpu())
            unet_outputs.append(unet_out.cpu())
            final_outputs.append(final_out.cpu())
    
    # 计算统计信息
    van_feat_all = torch.cat(van_features, dim=0)
    unet_feat_all = torch.cat(unet_features, dim=0)
    van_out_all = torch.cat(van_outputs, dim=0)
    unet_out_all = torch.cat(unet_outputs, dim=0)
    final_out_all = torch.cat(final_outputs, dim=0)
    
    stats = {
        'model_name': model_name,
        'van_feat_min': van_feat_all.min().item(),
        'van_feat_max': van_feat_all.max().item(),
        'van_feat_mean': van_feat_all.mean().item(),
        'van_feat_std': van_feat_all.std().item(),
        'unet_feat_min': unet_feat_all.min().item(),
        'unet_feat_max': unet_feat_all.max().item(),
        'unet_feat_mean': unet_feat_all.mean().item(),
        'unet_feat_std': unet_feat_all.std().item(),
        'van_out_min': van_out_all.min().item(),
        'van_out_max': van_out_all.max().item(),
        'van_out_mean': van_out_all.mean().item(),
        'unet_out_min': unet_out_all.min().item(),
        'unet_out_max': unet_out_all.max().item(),
        'unet_out_mean': unet_out_all.mean().item(),
        'final_out_min': final_out_all.min().item(),
        'final_out_max': final_out_all.max().item(),
        'final_out_mean': final_out_all.mean().item(),
    }
    
    return stats


def visualize_comparison(stats_list, save_dir='normalization_comparison'):
    """
    可视化不同模型的特征范围对比
    """
    os.makedirs(save_dir, exist_ok=True)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    model_names = [stats['model_name'] for stats in stats_list]
    colors = ['blue', 'red', 'green', 'orange', 'purple']
    
    # 1. 特征均值对比
    van_means = [stats['van_feat_mean'] for stats in stats_list]
    unet_means = [stats['unet_feat_mean'] for stats in stats_list]
    
    x_pos = np.arange(len(model_names))
    width = 0.35
    
    axes[0, 0].bar(x_pos - width/2, van_means, width, label='VAN Features', alpha=0.7, color='blue')
    axes[0, 0].bar(x_pos + width/2, unet_means, width, label='U-Net Features', alpha=0.7, color='red')
    axes[0, 0].set_title('Feature Mean Comparison')
    axes[0, 0].set_ylabel('Mean Value')
    axes[0, 0].set_xticks(x_pos)
    axes[0, 0].set_xticklabels(model_names, rotation=45)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 特征标准差对比
    van_stds = [stats['van_feat_std'] for stats in stats_list]
    unet_stds = [stats['unet_feat_std'] for stats in stats_list]
    
    axes[0, 1].bar(x_pos - width/2, van_stds, width, label='VAN Features', alpha=0.7, color='blue')
    axes[0, 1].bar(x_pos + width/2, unet_stds, width, label='U-Net Features', alpha=0.7, color='red')
    axes[0, 1].set_title('Feature Std Comparison')
    axes[0, 1].set_ylabel('Standard Deviation')
    axes[0, 1].set_xticks(x_pos)
    axes[0, 1].set_xticklabels(model_names, rotation=45)
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 输出范围对比
    van_out_ranges = [(stats['van_out_max'] - stats['van_out_min']) for stats in stats_list]
    unet_out_ranges = [(stats['unet_out_max'] - stats['unet_out_min']) for stats in stats_list]
    
    axes[1, 0].bar(x_pos - width/2, van_out_ranges, width, label='VAN Output', alpha=0.7, color='blue')
    axes[1, 0].bar(x_pos + width/2, unet_out_ranges, width, label='U-Net Output', alpha=0.7, color='red')
    axes[1, 0].set_title('Output Range Comparison')
    axes[1, 0].set_ylabel('Range (Max - Min)')
    axes[1, 0].set_xticks(x_pos)
    axes[1, 0].set_xticklabels(model_names, rotation=45)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 特征差异比率
    feat_ratios = []
    for stats in stats_list:
        van_range = stats['van_feat_max'] - stats['van_feat_min']
        unet_range = stats['unet_feat_max'] - stats['unet_feat_min']
        ratio = unet_range / (van_range + 1e-8)
        feat_ratios.append(ratio)
    
    axes[1, 1].bar(x_pos, feat_ratios, alpha=0.7, color=colors[:len(model_names)])
    axes[1, 1].axhline(y=1, color='black', linestyle='--', alpha=0.5, label='Equal Range')
    axes[1, 1].set_title('Feature Range Ratio (U-Net/VAN)')
    axes[1, 1].set_ylabel('Ratio')
    axes[1, 1].set_xticks(x_pos)
    axes[1, 1].set_xticklabels(model_names, rotation=45)
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, ratio in enumerate(feat_ratios):
        axes[1, 1].text(i, ratio + 0.1, f'{ratio:.2f}', ha='center', va='bottom')
    
    plt.suptitle('VCBNet Normalization Effect Comparison', fontsize=16)
    plt.tight_layout()
    
    save_path = os.path.join(save_dir, 'normalization_comparison.png')
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"Comparison visualization saved: {save_path}")


def print_detailed_stats(stats_list):
    """
    打印详细的统计信息
    """
    print("\n" + "="*80)
    print("DETAILED FEATURE RANGE ANALYSIS")
    print("="*80)
    
    for stats in stats_list:
        print(f"\n📊 {stats['model_name']}")
        print("-" * 50)
        
        print("VAN Branch Features:")
        print(f"  Range: {stats['van_feat_min']:.6f} ~ {stats['van_feat_max']:.6f}")
        print(f"  Mean: {stats['van_feat_mean']:.6f}, Std: {stats['van_feat_std']:.6f}")
        
        print("U-Net Branch Features:")
        print(f"  Range: {stats['unet_feat_min']:.6f} ~ {stats['unet_feat_max']:.6f}")
        print(f"  Mean: {stats['unet_feat_mean']:.6f}, Std: {stats['unet_feat_std']:.6f}")
        
        print("Output Comparison:")
        print(f"  VAN Output: {stats['van_out_min']:.6f} ~ {stats['van_out_max']:.6f} (mean: {stats['van_out_mean']:.6f})")
        print(f"  U-Net Output: {stats['unet_out_min']:.6f} ~ {stats['unet_out_max']:.6f} (mean: {stats['unet_out_mean']:.6f})")
        print(f"  Final Output: {stats['final_out_min']:.6f} ~ {stats['final_out_max']:.6f} (mean: {stats['final_out_mean']:.6f})")
        
        # 计算比率
        van_range = stats['van_feat_max'] - stats['van_feat_min']
        unet_range = stats['unet_feat_max'] - stats['unet_feat_min']
        range_ratio = unet_range / (van_range + 1e-8)
        
        mean_ratio = abs(stats['unet_feat_mean']) / (abs(stats['van_feat_mean']) + 1e-8)
        
        print(f"Feature Analysis:")
        print(f"  Range Ratio (U-Net/VAN): {range_ratio:.3f}")
        print(f"  Mean Ratio (U-Net/VAN): {mean_ratio:.3f}")
        
        if range_ratio < 2.0:
            print("  ✅ Good: Feature ranges are well balanced")
        elif range_ratio < 5.0:
            print("  ⚠️  Moderate: Some range imbalance")
        else:
            print("  ❌ Poor: Significant range imbalance")


def main():
    # 配置
    data_path = "data/test/202210090536_match.npy"
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    print("=== Testing VCBNet Normalization Effects ===")
    
    # 创建数据加载器
    img_size = (416, 448)
    dataset = RadarSets(data_path, img_size, mode='test', data_type='vis')
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # 测试不同的模型
    models_to_test = [
        ('Original VCBNet', VCBNet(n_channels=3, n_outputs=1)),
        ('Layer Norm', create_normalized_vcbnet('basic', norm_type='layer', fusion_weight=0.7)),
        ('Adaptive Norm', create_normalized_vcbnet('basic', norm_type='adaptive', fusion_weight=0.7)),
        ('Adaptive Fusion', create_normalized_vcbnet('adaptive', norm_type='adaptive')),
    ]
    
    stats_list = []
    
    for model_name, model in models_to_test:
        print(f"\n🔍 Testing {model_name}...")
        model = model.to(device)
        
        try:
            stats = analyze_feature_ranges(model, dataloader, device, model_name)
            stats_list.append(stats)
            print(f"✅ {model_name} analysis completed")
        except Exception as e:
            print(f"❌ {model_name} analysis failed: {e}")
            continue
    
    # 打印详细统计
    print_detailed_stats(stats_list)
    
    # 生成可视化对比
    if len(stats_list) > 1:
        print("\n📊 Generating comparison visualization...")
        visualize_comparison(stats_list)
    
    print("\n🎉 Analysis completed!")
    
    # 给出建议
    print("\n💡 Recommendations:")
    print("1. Look for models where U-Net/VAN range ratio is close to 1.0")
    print("2. Check if feature means are in similar scales")
    print("3. Adaptive normalization usually provides the best balance")


if __name__ == "__main__":
    main()
