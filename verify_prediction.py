#!/usr/bin/env python3
"""
预测结果验证脚本
针对样本202210090536，重新进行预测并与已保存的结果进行对比验证
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 导入模型和数据加载器
from model import VCBNet
from API.dataloader import RadarSets
from torch.utils.data import DataLoader


def load_model_and_predict(model_dir, data_path, device='auto'):
    """
    加载模型并进行预测
    """
    # 设备选择
    if device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)
    print(f"使用设备: {device}")
    
    # 1. 加载模型配置
    config_path = os.path.join(model_dir, 'model_param.json')
    with open(config_path, 'r') as f:
        config = json.load(f)
    print(f"模型配置: {config['model_type']}")
    
    # 2. 创建模型
    model = VCBNet(n_channels=config['in_shape'][0], n_outputs=config['out_chans'])
    model = model.to(device)
    
    # 3. 加载checkpoint
    checkpoint_path = os.path.join(model_dir, 'checkpoints', 'checkpoint.pth')
    print(f"加载checkpoint: {checkpoint_path}")
    
    checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
    state_dict = checkpoint['model']
    
    # 处理accelerator保存的模型（移除_orig_mod.前缀）
    new_state_dict = {}
    for key, value in state_dict.items():
        if key.startswith('_orig_mod.'):
            new_key = key[10:]
        else:
            new_key = key
        new_state_dict[new_key] = value
    
    # 加载权重
    missing_keys, unexpected_keys = model.load_state_dict(new_state_dict, strict=False)
    print(f"成功加载参数: {len(new_state_dict) - len(missing_keys)}/{len(new_state_dict)}")
    
    # 4. 创建数据集并预测
    img_size = (config['in_shape'][1], config['in_shape'][2])
    dataset = RadarSets(data_path, img_size, mode='test', data_type='vis')
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    model.eval()
    predictions = []
    
    with torch.no_grad():
        for inputs, _ in dataloader:
            inputs = inputs.to(device)
            outputs = model(inputs)
            pred_np = outputs.cpu().numpy()
            predictions.append(pred_np)
    
    return np.concatenate(predictions, axis=0) if predictions else None


def compare_predictions(new_pred, saved_pred_path, tolerance=1e-6):
    """
    比较新预测结果与已保存的结果
    """
    print(f"\n=== 预测结果对比 ===")
    
    # 加载已保存的预测结果
    if not os.path.exists(saved_pred_path):
        print(f"❌ 已保存的预测文件不存在: {saved_pred_path}")
        return False
    
    saved_pred = np.load(saved_pred_path)
    
    # 基本信息对比
    print(f"新预测形状: {new_pred.shape}")
    print(f"已保存形状: {saved_pred.shape}")
    
    if new_pred.shape != saved_pred.shape:
        print("❌ 形状不匹配！")
        return False
    
    # 数值对比
    print(f"\n数值统计对比:")
    print(f"新预测 - 最小值: {new_pred.min():.6f}, 最大值: {new_pred.max():.6f}, 均值: {new_pred.mean():.6f}")
    print(f"已保存 - 最小值: {saved_pred.min():.6f}, 最大值: {saved_pred.max():.6f}, 均值: {saved_pred.mean():.6f}")
    
    # 差异分析
    diff = np.abs(new_pred - saved_pred)
    max_diff = np.max(diff)
    mean_diff = np.mean(diff)
    
    print(f"\n差异分析:")
    print(f"最大绝对差异: {max_diff:.8f}")
    print(f"平均绝对差异: {mean_diff:.8f}")
    print(f"差异标准差: {np.std(diff):.8f}")
    
    # 判断是否一致
    is_identical = max_diff < tolerance
    
    if is_identical:
        print(f"✅ 预测结果一致！(容差: {tolerance})")
    else:
        print(f"❌ 预测结果不一致！最大差异 {max_diff:.8f} 超过容差 {tolerance}")
        
        # 分析差异分布
        print(f"\n差异分布:")
        print(f"差异 > 1e-6 的像素数: {np.sum(diff > 1e-6)}")
        print(f"差异 > 1e-5 的像素数: {np.sum(diff > 1e-5)}")
        print(f"差异 > 1e-4 的像素数: {np.sum(diff > 1e-4)}")
        print(f"差异 > 1e-3 的像素数: {np.sum(diff > 1e-3)}")
    
    return is_identical, diff


def visualize_comparison(new_pred, saved_pred, diff, sample_name, save_dir='verification_plots'):
    """
    可视化预测结果对比
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # New prediction result
    im1 = axes[0, 0].imshow(new_pred[0, 0] * 60, cmap='jet', vmin=0, vmax=70)
    axes[0, 0].set_title('New Prediction')
    axes[0, 0].axis('off')
    plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)

    # Saved prediction result
    im2 = axes[0, 1].imshow(saved_pred[0, 0] * 60, cmap='jet', vmin=0, vmax=70)
    axes[0, 1].set_title('Saved Prediction')
    axes[0, 1].axis('off')
    plt.colorbar(im2, ax=axes[0, 1], shrink=0.8)

    # Difference map
    im3 = axes[1, 0].imshow(diff[0, 0], cmap='hot')
    axes[1, 0].set_title(f'Absolute Difference\nMax: {np.max(diff):.2e}')
    axes[1, 0].axis('off')
    plt.colorbar(im3, ax=axes[1, 0], shrink=0.8)

    # Difference histogram
    axes[1, 1].hist(diff.flatten(), bins=50, alpha=0.7, edgecolor='black')
    axes[1, 1].set_xlabel('Absolute Difference')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('Difference Distribution')
    axes[1, 1].set_yscale('log')

    plt.suptitle(f'Prediction Verification - {sample_name}', fontsize=16)
    plt.tight_layout()
    
    save_path = os.path.join(save_dir, f'{sample_name}_verification.png')
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"保存对比图: {save_path}")


def main():
    # 配置参数
    sample_name = "202210090536"
    model_dir = "721/vcbnet0"
    data_path = f"data/test/{sample_name}_match.npy"
    saved_pred_path = f"predictions_vcbnet0/{sample_name}_match_pred.npy"
    
    print(f"=== 验证样本 {sample_name} 的预测结果 ===")
    
    # 检查文件是否存在
    if not os.path.exists(data_path):
        print(f"❌ 测试数据不存在: {data_path}")
        return
    
    if not os.path.exists(saved_pred_path):
        print(f"❌ 已保存的预测结果不存在: {saved_pred_path}")
        return
    
    # 1. 重新进行预测
    print("\n1. 重新进行预测...")
    try:
        new_pred = load_model_and_predict(model_dir, data_path)
        if new_pred is None:
            print("❌ 预测失败")
            return
        print(f"✅ 预测完成，形状: {new_pred.shape}")
    except Exception as e:
        print(f"❌ 预测过程出错: {e}")
        return
    
    # 2. 加载已保存的结果
    print("\n2. 加载已保存的预测结果...")
    saved_pred = np.load(saved_pred_path)
    print(f"✅ 已保存结果加载完成，形状: {saved_pred.shape}")
    
    # 3. 进行对比
    print("\n3. 进行结果对比...")
    is_identical, diff = compare_predictions(new_pred, saved_pred_path)
    
    # 4. 生成可视化对比
    print("\n4. 生成可视化对比...")
    visualize_comparison(new_pred, saved_pred, diff, sample_name)
    
    # 5. 总结
    print(f"\n=== 验证总结 ===")
    if is_identical:
        print("🎉 验证通过！新预测结果与已保存结果完全一致。")
        print("✅ 模型加载和预测流程正确")
        print("✅ 已保存的预测结果可信")
    else:
        print("⚠️  验证未通过！新预测结果与已保存结果存在差异。")
        print("可能原因:")
        print("- 模型权重加载不完整")
        print("- 随机种子不同")
        print("- 数据预处理差异")
        print("- 模型版本不匹配")


if __name__ == "__main__":
    main()
